{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3100, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3100, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3100, "tid": 24783, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3100, "tid": 24783, "ts": 1754737718758242, "dur": 600, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737718760955, "dur": 413, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3100, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717005198, "dur": 135987, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717141186, "dur": 1612503, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717141200, "dur": 46, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717141249, "dur": 97915, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717239178, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717239182, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717239218, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717239225, "dur": 1893, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241125, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241127, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241185, "dur": 4, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241190, "dur": 34, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241226, "dur": 39, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241267, "dur": 28, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241298, "dur": 37, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241337, "dur": 27, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241367, "dur": 33, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241402, "dur": 26, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241430, "dur": 22, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241454, "dur": 23, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241479, "dur": 23, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241504, "dur": 14, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241520, "dur": 20, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241541, "dur": 55, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241601, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241648, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241650, "dur": 48, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241701, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241703, "dur": 32, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241737, "dur": 26, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241768, "dur": 44, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241814, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241816, "dur": 41, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241859, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241861, "dur": 37, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241901, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241902, "dur": 39, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241945, "dur": 29, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717241975, "dur": 26, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242003, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242004, "dur": 25, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242030, "dur": 29, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242062, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242063, "dur": 34, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242100, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242138, "dur": 26, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242166, "dur": 24, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242192, "dur": 22, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242215, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242235, "dur": 22, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242260, "dur": 24, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242285, "dur": 22, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242309, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242333, "dur": 24, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242359, "dur": 23, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242384, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242385, "dur": 38, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242427, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242429, "dur": 84, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242515, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242517, "dur": 33, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242552, "dur": 27, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242580, "dur": 19, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242600, "dur": 25, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242627, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242650, "dur": 46, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242697, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242721, "dur": 18, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242740, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242763, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242785, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242809, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242831, "dur": 23, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242856, "dur": 18, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242876, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242898, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242920, "dur": 37, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242958, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717242980, "dur": 18, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243001, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243023, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243046, "dur": 18, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243065, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243088, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243110, "dur": 22, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243134, "dur": 21, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243156, "dur": 22, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243180, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243202, "dur": 22, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243226, "dur": 21, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243248, "dur": 22, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243272, "dur": 17, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243291, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243309, "dur": 41, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243352, "dur": 23, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243376, "dur": 25, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243402, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243425, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243449, "dur": 21, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243472, "dur": 20, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243494, "dur": 22, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243517, "dur": 22, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243541, "dur": 22, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243564, "dur": 18, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243584, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243606, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243629, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243652, "dur": 17, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243670, "dur": 39, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243711, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243735, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243758, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243781, "dur": 23, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243805, "dur": 25, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243832, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243855, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243879, "dur": 22, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243903, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243926, "dur": 23, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243951, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243974, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717243997, "dur": 18, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244017, "dur": 21, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244039, "dur": 18, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244058, "dur": 48, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244108, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244141, "dur": 27, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244170, "dur": 19, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244191, "dur": 21, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244213, "dur": 18, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244233, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244256, "dur": 18, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244276, "dur": 39, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244316, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244339, "dur": 26, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244367, "dur": 22, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244391, "dur": 23, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244415, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244437, "dur": 50, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244490, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244492, "dur": 40, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244535, "dur": 25, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244561, "dur": 44, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244607, "dur": 52, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244662, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244664, "dur": 40, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244706, "dur": 27, "ph": "X", "name": "ReadAsync 1087", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244735, "dur": 29, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244766, "dur": 20, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244788, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244811, "dur": 23, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244835, "dur": 28, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244865, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244888, "dur": 16, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244906, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244929, "dur": 21, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244951, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244974, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717244996, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245019, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245042, "dur": 24, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245067, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245090, "dur": 21, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245112, "dur": 18, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245132, "dur": 21, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245154, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245179, "dur": 24, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245204, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245230, "dur": 18, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245250, "dur": 14, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245265, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245287, "dur": 23, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245312, "dur": 28, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245342, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245343, "dur": 36, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245382, "dur": 25, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245409, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245433, "dur": 22, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245456, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245472, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245496, "dur": 18, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245515, "dur": 28, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245545, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245569, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245591, "dur": 22, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245615, "dur": 29, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245647, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245648, "dur": 30, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245680, "dur": 43, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245726, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245727, "dur": 40, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245771, "dur": 36, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245811, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245813, "dur": 37, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245854, "dur": 37, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245893, "dur": 32, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245926, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245948, "dur": 19, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245968, "dur": 23, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717245992, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246016, "dur": 22, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246040, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246063, "dur": 22, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246087, "dur": 22, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246111, "dur": 22, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246135, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246158, "dur": 22, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246182, "dur": 18, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246201, "dur": 22, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246225, "dur": 22, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246248, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246272, "dur": 25, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246299, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246322, "dur": 22, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246345, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246368, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246391, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246414, "dur": 23, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246439, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246441, "dur": 40, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246483, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246485, "dur": 44, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246530, "dur": 28, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246560, "dur": 20, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246583, "dur": 22, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246607, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246630, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246652, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246675, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246694, "dur": 22, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246718, "dur": 24, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246744, "dur": 18, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246764, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246787, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246811, "dur": 22, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246834, "dur": 23, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246858, "dur": 115, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717246977, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247019, "dur": 28, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247049, "dur": 19, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247069, "dur": 24, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247096, "dur": 23, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247121, "dur": 18, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247140, "dur": 28, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247169, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247193, "dur": 22, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247216, "dur": 21, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247239, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247261, "dur": 23, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247285, "dur": 24, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247311, "dur": 18, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247331, "dur": 18, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247350, "dur": 18, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247369, "dur": 27, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247398, "dur": 31, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247432, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247433, "dur": 41, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247477, "dur": 29, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247508, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247526, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247550, "dur": 23, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247575, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247597, "dur": 32, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247631, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247655, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247674, "dur": 15, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247690, "dur": 18, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247710, "dur": 27, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247738, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247761, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247783, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247806, "dur": 18, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247825, "dur": 20, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247846, "dur": 21, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247869, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247893, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247916, "dur": 22, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247939, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247961, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247963, "dur": 21, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717247986, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248008, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248030, "dur": 20, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248052, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248075, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248098, "dur": 23, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248123, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248145, "dur": 20, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248166, "dur": 23, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248191, "dur": 21, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248213, "dur": 19, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248234, "dur": 18, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248254, "dur": 22, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248277, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248299, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248322, "dur": 22, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248346, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248372, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248395, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248416, "dur": 30, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248449, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248451, "dur": 40, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248494, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248495, "dur": 41, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248539, "dur": 34, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248575, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248577, "dur": 38, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248617, "dur": 29, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248648, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248650, "dur": 34, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248686, "dur": 25, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248713, "dur": 22, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248737, "dur": 24, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248762, "dur": 23, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248787, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248809, "dur": 24, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248835, "dur": 33, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248870, "dur": 18, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248890, "dur": 21, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248912, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248936, "dur": 22, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248960, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717248983, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249006, "dur": 23, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249030, "dur": 18, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249050, "dur": 22, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249074, "dur": 17, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249092, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249119, "dur": 22, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249142, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249164, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249187, "dur": 22, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249210, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249234, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249257, "dur": 22, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249280, "dur": 18, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249299, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249322, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249345, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249367, "dur": 23, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249392, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249414, "dur": 22, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249438, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249461, "dur": 24, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249486, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249510, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249533, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249556, "dur": 22, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249579, "dur": 18, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249598, "dur": 22, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249622, "dur": 22, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249646, "dur": 22, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249670, "dur": 20, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249691, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249715, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249737, "dur": 21, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249760, "dur": 22, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249784, "dur": 24, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249809, "dur": 14, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249825, "dur": 18, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249844, "dur": 22, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249868, "dur": 21, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249891, "dur": 55, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249951, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717249954, "dur": 60, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250017, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250019, "dur": 40, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250062, "dur": 44, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250107, "dur": 41, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250151, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250153, "dur": 33, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250189, "dur": 25, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250218, "dur": 30, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250250, "dur": 25, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250277, "dur": 22, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250301, "dur": 19, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250321, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250346, "dur": 34, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250381, "dur": 24, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250407, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250432, "dur": 17, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250451, "dur": 23, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250475, "dur": 23, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250499, "dur": 24, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250525, "dur": 24, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250550, "dur": 36, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250591, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250594, "dur": 43, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250640, "dur": 1, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250641, "dur": 37, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250681, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250684, "dur": 46, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250732, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250733, "dur": 45, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250781, "dur": 39, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250822, "dur": 46, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250871, "dur": 30, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250903, "dur": 27, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250934, "dur": 34, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250971, "dur": 26, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717250999, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251001, "dur": 38, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251043, "dur": 39, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251086, "dur": 33, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251121, "dur": 46, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251171, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251174, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251213, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251215, "dur": 33, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251250, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251251, "dur": 31, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251285, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251287, "dur": 37, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251327, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251328, "dur": 38, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251369, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251370, "dur": 36, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251422, "dur": 46, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251469, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251471, "dur": 34, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251506, "dur": 22, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251532, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251534, "dur": 38, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251574, "dur": 29, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251606, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251608, "dur": 43, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251654, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251656, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251703, "dur": 30, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251734, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251735, "dur": 29, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251769, "dur": 42, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251813, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251815, "dur": 37, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251853, "dur": 24, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251878, "dur": 23, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251904, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251929, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251953, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717251982, "dur": 23, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252006, "dur": 26, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252035, "dur": 24, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252061, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252091, "dur": 30, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252123, "dur": 31, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252157, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252159, "dur": 38, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252199, "dur": 26, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252227, "dur": 22, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252250, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252272, "dur": 24, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252300, "dur": 53, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252354, "dur": 28, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252385, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252387, "dur": 26, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252414, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252416, "dur": 34, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252454, "dur": 42, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252501, "dur": 41, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252545, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252547, "dur": 35, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252585, "dur": 24, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252610, "dur": 23, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252635, "dur": 31, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252667, "dur": 22, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252690, "dur": 28, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252719, "dur": 25, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252746, "dur": 28, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252776, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252799, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252824, "dur": 20, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252845, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252870, "dur": 22, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252893, "dur": 21, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252916, "dur": 21, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252939, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252961, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717252985, "dur": 18, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253004, "dur": 18, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253023, "dur": 18, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253043, "dur": 22, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253066, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253089, "dur": 21, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253111, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253135, "dur": 22, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253158, "dur": 18, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253178, "dur": 18, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253197, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253198, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253221, "dur": 22, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253244, "dur": 18, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253264, "dur": 21, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253286, "dur": 21, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253308, "dur": 22, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253331, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253354, "dur": 22, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253377, "dur": 18, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253396, "dur": 22, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253420, "dur": 29, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253452, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253453, "dur": 41, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253496, "dur": 2, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253498, "dur": 30, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253532, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253533, "dur": 44, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253581, "dur": 40, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253623, "dur": 25, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253651, "dur": 22, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253674, "dur": 31, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253707, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253733, "dur": 26, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253760, "dur": 24, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253785, "dur": 29, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253817, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253818, "dur": 36, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253857, "dur": 39, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253897, "dur": 24, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253923, "dur": 21, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253946, "dur": 23, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253970, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717253993, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254017, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254041, "dur": 22, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254064, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254086, "dur": 23, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254111, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254134, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254157, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254180, "dur": 24, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254205, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254229, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254252, "dur": 17, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254271, "dur": 21, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254294, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254316, "dur": 24, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254342, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254365, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254387, "dur": 20, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254409, "dur": 38, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254449, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254451, "dur": 41, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254496, "dur": 35, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254534, "dur": 31, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254569, "dur": 28, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254598, "dur": 24, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254624, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254647, "dur": 23, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254671, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254672, "dur": 23, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254697, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254720, "dur": 18, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254739, "dur": 22, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254762, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254785, "dur": 20, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254807, "dur": 18, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254826, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254849, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254871, "dur": 22, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254895, "dur": 22, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254918, "dur": 22, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254942, "dur": 22, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254965, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717254991, "dur": 22, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255014, "dur": 48, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255064, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255092, "dur": 30, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255124, "dur": 26, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255151, "dur": 22, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255175, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255198, "dur": 22, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255221, "dur": 30, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255252, "dur": 28, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255282, "dur": 29, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255312, "dur": 20, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255334, "dur": 18, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255354, "dur": 22, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255377, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255401, "dur": 20, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255422, "dur": 20, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255445, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255470, "dur": 24, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255497, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255520, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255542, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255565, "dur": 18, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255584, "dur": 20, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255606, "dur": 22, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255630, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255653, "dur": 17, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255672, "dur": 19, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255693, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255717, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255740, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255742, "dur": 22, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255766, "dur": 20, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255787, "dur": 17, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255805, "dur": 22, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255829, "dur": 20, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255851, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255875, "dur": 18, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255894, "dur": 21, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255916, "dur": 21, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255938, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255961, "dur": 22, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717255984, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256008, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256031, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256054, "dur": 22, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256077, "dur": 21, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256100, "dur": 17, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256118, "dur": 19, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256139, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256161, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256184, "dur": 21, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256206, "dur": 22, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256229, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256230, "dur": 16, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256248, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256271, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256293, "dur": 21, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256315, "dur": 17, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256333, "dur": 21, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256356, "dur": 21, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256378, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256401, "dur": 21, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256423, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256444, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256467, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256489, "dur": 18, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256509, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256531, "dur": 18, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256551, "dur": 18, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256570, "dur": 23, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256595, "dur": 20, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256616, "dur": 21, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256639, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256661, "dur": 21, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256684, "dur": 22, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256707, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256730, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256754, "dur": 18, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256773, "dur": 21, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256794, "dur": 19, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256815, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256838, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256857, "dur": 22, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256881, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256904, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256923, "dur": 23, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256948, "dur": 22, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256971, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717256993, "dur": 19, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257014, "dur": 17, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257033, "dur": 21, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257055, "dur": 21, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257078, "dur": 27, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257106, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257129, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257151, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257174, "dur": 25, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257200, "dur": 18, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257220, "dur": 20, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257241, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257264, "dur": 26, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257292, "dur": 21, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257315, "dur": 20, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257336, "dur": 20, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257357, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257380, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257404, "dur": 22, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257427, "dur": 36, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257468, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257470, "dur": 75, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257550, "dur": 2, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257553, "dur": 60, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257619, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257622, "dur": 85, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257712, "dur": 2, "ph": "X", "name": "ProcessMessages 960", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257715, "dur": 70, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257788, "dur": 1, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257790, "dur": 40, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257832, "dur": 49, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257885, "dur": 40, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257928, "dur": 24, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257954, "dur": 19, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257975, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717257999, "dur": 23, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258023, "dur": 23, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258047, "dur": 41, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258090, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258092, "dur": 34, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258128, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258153, "dur": 22, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258177, "dur": 20, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258199, "dur": 22, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258223, "dur": 22, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258247, "dur": 21, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258269, "dur": 21, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258292, "dur": 24, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258317, "dur": 21, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258340, "dur": 17, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258358, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258382, "dur": 21, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258405, "dur": 22, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258429, "dur": 26, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258456, "dur": 34, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258493, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258494, "dur": 37, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258534, "dur": 27, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258562, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258585, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258607, "dur": 22, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258631, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258654, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258676, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258700, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258722, "dur": 22, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258746, "dur": 15, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258762, "dur": 28, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258792, "dur": 20, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258813, "dur": 24, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258838, "dur": 21, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258861, "dur": 28, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258890, "dur": 21, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258913, "dur": 20, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258935, "dur": 22, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258958, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717258980, "dur": 20, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259002, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259026, "dur": 21, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259048, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259071, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259092, "dur": 20, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259114, "dur": 21, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259136, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259157, "dur": 20, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259179, "dur": 22, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259204, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259226, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259248, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259270, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259293, "dur": 23, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259318, "dur": 20, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259339, "dur": 16, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259357, "dur": 21, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259379, "dur": 35, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259416, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259440, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259442, "dur": 37, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259482, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259483, "dur": 39, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259525, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259527, "dur": 40, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259569, "dur": 26, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259597, "dur": 25, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259624, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259626, "dur": 30, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259658, "dur": 23, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259683, "dur": 28, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259715, "dur": 83, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259801, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259840, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259841, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259883, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259885, "dur": 34, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259922, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259924, "dur": 38, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259963, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259965, "dur": 31, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259998, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717259999, "dur": 34, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260035, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260036, "dur": 26, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260066, "dur": 27, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260096, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260124, "dur": 20, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260146, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260170, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260197, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260199, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260235, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260237, "dur": 27, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260267, "dur": 28, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260297, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260298, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260331, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260333, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260362, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260364, "dur": 25, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260391, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260416, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260418, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260450, "dur": 26, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260479, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260508, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260509, "dur": 36, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260547, "dur": 19, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260568, "dur": 22, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260593, "dur": 27, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260622, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260624, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260655, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260657, "dur": 34, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260694, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260721, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260750, "dur": 22, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260774, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260776, "dur": 25, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260802, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260831, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260857, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260885, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260915, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260941, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260969, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260994, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717260996, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261028, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261030, "dur": 29, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261060, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261062, "dur": 25, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261088, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261090, "dur": 24, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261116, "dur": 23, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261142, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261169, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261197, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261223, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261227, "dur": 28, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261258, "dur": 22, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261282, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261309, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261339, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261367, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717261395, "dur": 645, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717262044, "dur": 46, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717262092, "dur": 2518, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717264614, "dur": 70, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717264688, "dur": 46, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717264736, "dur": 40, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717264780, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717264781, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717264815, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717264873, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717264904, "dur": 5055, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717269969, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717269974, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270043, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270046, "dur": 57, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270106, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270148, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270391, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270437, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270439, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270492, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270528, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270530, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270563, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270616, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270702, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270814, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270816, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270860, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270895, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270935, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717270937, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271047, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271073, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271098, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271142, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271178, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271180, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271245, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271283, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271287, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271319, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271348, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271350, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271388, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271390, "dur": 37, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271430, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271432, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271472, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271474, "dur": 80, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271558, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271561, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271616, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271620, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271657, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271659, "dur": 107, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271770, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271807, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271809, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271852, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271854, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271887, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271930, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271932, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271967, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717271992, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272036, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272082, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272084, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272128, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272130, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272183, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272218, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272243, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272305, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272339, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272442, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272467, "dur": 3, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272471, "dur": 32, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272507, "dur": 31, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272540, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272566, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272590, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272677, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272708, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272746, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272766, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272787, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272820, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717272972, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273001, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273035, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273073, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273104, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273188, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273219, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273252, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273276, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273323, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273348, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273385, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273461, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273510, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273673, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273701, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273736, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273759, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273788, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717273977, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274006, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274036, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274073, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274093, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274128, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274147, "dur": 222, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274370, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274395, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274422, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274445, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274474, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274496, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274534, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274563, "dur": 165, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274730, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274764, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274765, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274812, "dur": 36, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274851, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274878, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274935, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717274965, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717275025, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717275052, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717275109, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717275137, "dur": 91, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717275234, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717275274, "dur": 268524, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717543809, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717543813, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717543844, "dur": 24, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717543869, "dur": 3615, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717547494, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717547499, "dur": 153, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717547657, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717547659, "dur": 345, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717548008, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717548010, "dur": 46, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717548060, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717548062, "dur": 429, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717548495, "dur": 602, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717549101, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717549104, "dur": 964, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717550072, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717550074, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717550131, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717550133, "dur": 60, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717550197, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717550199, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717550230, "dur": 432, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717550668, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717550699, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717550701, "dur": 1115, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717551820, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717551822, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717551866, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717551868, "dur": 169, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552043, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552101, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552104, "dur": 27, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552132, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552161, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552163, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552234, "dur": 121, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552359, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552364, "dur": 33, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552401, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552404, "dur": 44, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552451, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552453, "dur": 111, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552566, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552568, "dur": 160, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552732, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552735, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552795, "dur": 3, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552800, "dur": 41, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552844, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552846, "dur": 44, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552893, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552897, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552935, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552968, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717552970, "dur": 112, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717553084, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717553118, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717553149, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717553234, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717553267, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717553297, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717553319, "dur": 21402, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717574732, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717574737, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717574775, "dur": 26, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717574802, "dur": 625, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717575434, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717575440, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717575501, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717575506, "dur": 256737, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717832254, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717832259, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717832288, "dur": 2984, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717835277, "dur": 73, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717835354, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717835358, "dur": 8186, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717843551, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717843554, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717843609, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737717843611, "dur": 275037, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718118659, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718118663, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718118702, "dur": 24, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718118727, "dur": 5655, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718124390, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718124394, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718124433, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718124436, "dur": 1658, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718126101, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718126104, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718126157, "dur": 27, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718126185, "dur": 10685, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718136880, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718136886, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718136946, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718136951, "dur": 673, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718137629, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718137631, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718137684, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718137709, "dur": 30567, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718168287, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718168291, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718168329, "dur": 2923, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718171258, "dur": 1481, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718172744, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718172747, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718172794, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718172797, "dur": 22357, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718195164, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718195169, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718195218, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718195222, "dur": 271899, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718467133, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718467138, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718467206, "dur": 29, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718467236, "dur": 1906, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718469147, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718469190, "dur": 38, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718469228, "dur": 4432, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718473667, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718473669, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718473725, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718473729, "dur": 797, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718474531, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718474577, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718474595, "dur": 13389, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718487995, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718488000, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718488036, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718488040, "dur": 8646, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718496697, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718496704, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718496761, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718496765, "dur": 637, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718497407, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718497447, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718497473, "dur": 230563, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718728046, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718728050, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718728106, "dur": 32, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718728139, "dur": 5158, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718733303, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718733306, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718733377, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718733381, "dur": 869, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718734255, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718734290, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718734311, "dur": 10825, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718745142, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718745145, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718745172, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718745175, "dur": 855, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718746034, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718746036, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718746092, "dur": 25, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718746118, "dur": 531, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718746651, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718746654, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754737718746695, "dur": 6990, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737718761371, "dur": 939, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 17179869184, "ts": 1754737717005160, "dur": 7, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 17179869184, "ts": 1754737717005168, "dur": 136017, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 17179869184, "ts": 1754737717141186, "dur": 59, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737718762311, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3100, "tid": 1, "ts": 1754737716108665, "dur": 3052, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754737716111721, "dur": 19641, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754737716131370, "dur": 61702, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737718762316, "dur": 4, "ph": "X", "name": "", "args": {}}, {"pid": 3100, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716107665, "dur": 137066, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716244733, "dur": 28692, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716245432, "dur": 849, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716246287, "dur": 862, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247152, "dur": 133, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247288, "dur": 243, "ph": "X", "name": "ProcessMessages 10727", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247533, "dur": 53, "ph": "X", "name": "ReadAsync 10727", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247587, "dur": 1, "ph": "X", "name": "ProcessMessages 4041", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247589, "dur": 30, "ph": "X", "name": "ReadAsync 4041", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247647, "dur": 55, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247705, "dur": 23, "ph": "X", "name": "ReadAsync 1464", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247728, "dur": 23, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247753, "dur": 25, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247779, "dur": 39, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247821, "dur": 23, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247845, "dur": 23, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247870, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247918, "dur": 50, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247970, "dur": 21, "ph": "X", "name": "ReadAsync 1286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716247993, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248016, "dur": 19, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248037, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248059, "dur": 23, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248083, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248106, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248128, "dur": 22, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248152, "dur": 45, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248198, "dur": 63, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248264, "dur": 1, "ph": "X", "name": "ProcessMessages 1538", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248266, "dur": 39, "ph": "X", "name": "ReadAsync 1538", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248307, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248308, "dur": 29, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248339, "dur": 23, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248365, "dur": 24, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248390, "dur": 58, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248449, "dur": 22, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248473, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248497, "dur": 18, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248516, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248536, "dur": 48, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248586, "dur": 24, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248611, "dur": 18, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248656, "dur": 28, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248685, "dur": 49, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248736, "dur": 63, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248801, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248803, "dur": 44, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248849, "dur": 22, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248873, "dur": 21, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248896, "dur": 22, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248919, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716248942, "dur": 80, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249024, "dur": 22, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249048, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249070, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249096, "dur": 19, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249117, "dur": 21, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249139, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249162, "dur": 18, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249182, "dur": 21, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249204, "dur": 22, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249227, "dur": 88, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249316, "dur": 76, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249394, "dur": 29, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249424, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249448, "dur": 23, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249472, "dur": 19, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249492, "dur": 21, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249514, "dur": 22, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249538, "dur": 22, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249561, "dur": 25, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249587, "dur": 60, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249649, "dur": 20, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249670, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249693, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249715, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249737, "dur": 19, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249757, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249780, "dur": 54, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249835, "dur": 22, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249858, "dur": 18, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249878, "dur": 64, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716249985, "dur": 29, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250016, "dur": 21, "ph": "X", "name": "ReadAsync 1004", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250039, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250061, "dur": 28, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250090, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250114, "dur": 56, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250172, "dur": 21, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250194, "dur": 22, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250217, "dur": 21, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250240, "dur": 62, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250304, "dur": 27, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250332, "dur": 57, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250390, "dur": 24, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250416, "dur": 17, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250435, "dur": 21, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250458, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250481, "dur": 21, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250503, "dur": 21, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250526, "dur": 23, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250550, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250574, "dur": 65, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250641, "dur": 31, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250674, "dur": 21, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250696, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250720, "dur": 21, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250743, "dur": 20, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250764, "dur": 23, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250788, "dur": 22, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250811, "dur": 47, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250860, "dur": 21, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250883, "dur": 102, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716250986, "dur": 26, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251013, "dur": 21, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251036, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251058, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251080, "dur": 61, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251143, "dur": 20, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251164, "dur": 39, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251204, "dur": 29, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251235, "dur": 31, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251267, "dur": 60, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251329, "dur": 42, "ph": "X", "name": "ReadAsync 1323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251373, "dur": 43, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251417, "dur": 67, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251489, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251492, "dur": 63, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251555, "dur": 1, "ph": "X", "name": "ProcessMessages 1599", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251557, "dur": 42, "ph": "X", "name": "ReadAsync 1599", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251601, "dur": 31, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251635, "dur": 32, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251669, "dur": 37, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251709, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251710, "dur": 26, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251764, "dur": 57, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251823, "dur": 23, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251848, "dur": 25, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251875, "dur": 22, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251899, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251921, "dur": 21, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251944, "dur": 18, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251964, "dur": 14, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251979, "dur": 18, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716251999, "dur": 21, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252048, "dur": 68, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252117, "dur": 24, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252143, "dur": 22, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252166, "dur": 21, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252189, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252212, "dur": 46, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252260, "dur": 20, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252281, "dur": 154, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252439, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252470, "dur": 20, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252491, "dur": 23, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252516, "dur": 52, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252569, "dur": 21, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252592, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252613, "dur": 18, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252633, "dur": 43, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252678, "dur": 23, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252702, "dur": 21, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252725, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252748, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252771, "dur": 17, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252789, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252835, "dur": 14, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252850, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252872, "dur": 21, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252895, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252917, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252939, "dur": 21, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252961, "dur": 22, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716252985, "dur": 21, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253007, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253029, "dur": 45, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253076, "dur": 66, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253144, "dur": 23, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253169, "dur": 21, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253191, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253214, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253237, "dur": 43, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253281, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253304, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253326, "dur": 14, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253341, "dur": 51, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253394, "dur": 21, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253416, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253439, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253461, "dur": 20, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253483, "dur": 14, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253499, "dur": 46, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253546, "dur": 47, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253595, "dur": 46, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253643, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253645, "dur": 41, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253689, "dur": 55, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253745, "dur": 25, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253772, "dur": 27, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253803, "dur": 32, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253836, "dur": 22, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253861, "dur": 23, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253885, "dur": 55, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253941, "dur": 27, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253970, "dur": 23, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716253995, "dur": 21, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254017, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254041, "dur": 43, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254086, "dur": 21, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254140, "dur": 55, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254197, "dur": 27, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254226, "dur": 22, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254249, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254272, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254291, "dur": 20, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254312, "dur": 21, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254335, "dur": 23, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254359, "dur": 45, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254405, "dur": 18, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254425, "dur": 52, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254504, "dur": 41, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254546, "dur": 22, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254570, "dur": 21, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254592, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254614, "dur": 47, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254663, "dur": 21, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254686, "dur": 121, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254808, "dur": 63, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254872, "dur": 1, "ph": "X", "name": "ProcessMessages 2453", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254874, "dur": 22, "ph": "X", "name": "ReadAsync 2453", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254898, "dur": 18, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254917, "dur": 24, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254942, "dur": 14, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254958, "dur": 20, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716254979, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255003, "dur": 22, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255026, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255049, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255072, "dur": 49, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255122, "dur": 48, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255172, "dur": 18, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255191, "dur": 21, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255214, "dur": 43, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255259, "dur": 24, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255284, "dur": 20, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255306, "dur": 22, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255329, "dur": 21, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255352, "dur": 47, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255401, "dur": 49, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255451, "dur": 26, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255479, "dur": 20, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255500, "dur": 53, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255555, "dur": 22, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255578, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255601, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255620, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255639, "dur": 23, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255666, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255726, "dur": 24, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255751, "dur": 47, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255800, "dur": 21, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255822, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255845, "dur": 21, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255868, "dur": 21, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255890, "dur": 17, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255908, "dur": 21, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255931, "dur": 18, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255950, "dur": 21, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255972, "dur": 21, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716255994, "dur": 45, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256041, "dur": 45, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256088, "dur": 20, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256109, "dur": 21, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256152, "dur": 29, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256183, "dur": 47, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256231, "dur": 23, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256255, "dur": 21, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256278, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256300, "dur": 30, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256333, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256380, "dur": 18, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256399, "dur": 22, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256423, "dur": 21, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256446, "dur": 22, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256470, "dur": 19, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256490, "dur": 71, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256562, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256603, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256640, "dur": 22, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256663, "dur": 76, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256740, "dur": 22, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256764, "dur": 17, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256783, "dur": 20, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256805, "dur": 20, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256827, "dur": 51, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256880, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256902, "dur": 23, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256926, "dur": 22, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716256949, "dur": 49, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716257000, "dur": 67, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716257068, "dur": 25, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716257095, "dur": 20, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716257116, "dur": 54, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716257931, "dur": 64, "ph": "X", "name": "ReadAsync 2", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716257996, "dur": 146, "ph": "X", "name": "ProcessMessages 6459", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258143, "dur": 34, "ph": "X", "name": "ReadAsync 6459", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258179, "dur": 24, "ph": "X", "name": "ReadAsync 1371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258204, "dur": 45, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258251, "dur": 22, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258303, "dur": 25, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258330, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258353, "dur": 53, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258408, "dur": 18, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258427, "dur": 58, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258487, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258511, "dur": 26, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258538, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258560, "dur": 36, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258600, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258601, "dur": 232, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258838, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258874, "dur": 24, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258900, "dur": 57, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258959, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716258960, "dur": 50, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259013, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259049, "dur": 24, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259077, "dur": 29, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259109, "dur": 24, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259135, "dur": 58, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259195, "dur": 17, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259213, "dur": 22, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259237, "dur": 22, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259260, "dur": 75, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259337, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259360, "dur": 32, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259396, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259429, "dur": 23, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259482, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259508, "dur": 22, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259531, "dur": 22, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259555, "dur": 43, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259600, "dur": 21, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259623, "dur": 80, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259705, "dur": 22, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259728, "dur": 22, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259752, "dur": 21, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259774, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259797, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259818, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259885, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259941, "dur": 28, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259972, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716259974, "dur": 36, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260012, "dur": 69, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260084, "dur": 31, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260117, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260118, "dur": 24, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260144, "dur": 71, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260218, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260248, "dur": 23, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260272, "dur": 28, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260303, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260304, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260362, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260389, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260413, "dur": 28, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260443, "dur": 53, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260498, "dur": 24, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260526, "dur": 30, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260559, "dur": 21, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260582, "dur": 21, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260605, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260627, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260650, "dur": 22, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260674, "dur": 56, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260732, "dur": 53, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260786, "dur": 55, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260843, "dur": 19, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260864, "dur": 22, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260887, "dur": 17, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260905, "dur": 53, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260962, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716260996, "dur": 23, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261021, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261043, "dur": 27, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261072, "dur": 64, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261138, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261198, "dur": 48, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261247, "dur": 28, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261277, "dur": 30, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261311, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261372, "dur": 24, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261398, "dur": 44, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261444, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261500, "dur": 20, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261522, "dur": 24, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261547, "dur": 56, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261604, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261654, "dur": 25, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261681, "dur": 27, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261709, "dur": 24, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261734, "dur": 21, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261757, "dur": 21, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261780, "dur": 17, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261844, "dur": 21, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261866, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261885, "dur": 41, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261927, "dur": 51, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716261980, "dur": 23, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262004, "dur": 21, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262027, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262049, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262071, "dur": 17, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262090, "dur": 14, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262105, "dur": 68, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262174, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262198, "dur": 20, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262220, "dur": 53, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262274, "dur": 47, "ph": "X", "name": "ReadAsync 943", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262323, "dur": 21, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262346, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262368, "dur": 26, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262395, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262455, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262478, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262500, "dur": 21, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262523, "dur": 20, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262544, "dur": 63, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262609, "dur": 22, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262632, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262655, "dur": 22, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262678, "dur": 17, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262697, "dur": 67, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262766, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262792, "dur": 21, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262814, "dur": 17, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262833, "dur": 57, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262892, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716262972, "dur": 51, "ph": "X", "name": "ReadAsync 1183", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263024, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263092, "dur": 23, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263117, "dur": 56, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263175, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263201, "dur": 21, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263223, "dur": 21, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263246, "dur": 45, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263295, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263298, "dur": 71, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263374, "dur": 2, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263377, "dur": 109, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263490, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263491, "dur": 85, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263578, "dur": 38, "ph": "X", "name": "ReadAsync 1169", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263619, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263657, "dur": 24, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263682, "dur": 19, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263703, "dur": 53, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263757, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263823, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263848, "dur": 44, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263893, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716263970, "dur": 30, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264003, "dur": 32, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264036, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264061, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264083, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264106, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264128, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264151, "dur": 21, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264222, "dur": 31, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264255, "dur": 17, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264273, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264337, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264359, "dur": 21, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264382, "dur": 139, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264523, "dur": 23, "ph": "X", "name": "ReadAsync 1376", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264547, "dur": 54, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264602, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264625, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264649, "dur": 59, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264710, "dur": 26, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264737, "dur": 62, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264801, "dur": 21, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264824, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264882, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264898, "dur": 18, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264917, "dur": 22, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264940, "dur": 18, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716264960, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265059, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265145, "dur": 20, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265167, "dur": 165, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265334, "dur": 28, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265363, "dur": 21, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265386, "dur": 29, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265419, "dur": 37, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265459, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265460, "dur": 35, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265498, "dur": 24, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265524, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265546, "dur": 51, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265599, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265623, "dur": 125, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265750, "dur": 25, "ph": "X", "name": "ReadAsync 1947", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265776, "dur": 17, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265795, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265817, "dur": 20, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265839, "dur": 87, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265927, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265954, "dur": 22, "ph": "X", "name": "ReadAsync 1064", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716265977, "dur": 21, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266000, "dur": 21, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266022, "dur": 71, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266094, "dur": 22, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266118, "dur": 43, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266162, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266185, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266211, "dur": 21, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266233, "dur": 22, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266257, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266279, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266302, "dur": 61, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266364, "dur": 65, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266431, "dur": 70, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266503, "dur": 20, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266525, "dur": 75, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266603, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266641, "dur": 34, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266677, "dur": 24, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266763, "dur": 29, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266794, "dur": 20, "ph": "X", "name": "ReadAsync 1212", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266816, "dur": 60, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266878, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716266901, "dur": 713, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716267615, "dur": 2, "ph": "X", "name": "ProcessMessages 6041", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716267618, "dur": 25, "ph": "X", "name": "ReadAsync 6041", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716267645, "dur": 25, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716267671, "dur": 22, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716267694, "dur": 22, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716267718, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716267741, "dur": 67, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716267809, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716267833, "dur": 112, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716267946, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716267976, "dur": 304, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716268282, "dur": 105, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716268390, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716268393, "dur": 272, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716268667, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716268693, "dur": 148, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754737716268842, "dur": 4532, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737718762321, "dur": 410, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 8589934592, "ts": 1754737716105899, "dur": 87271, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 8589934592, "ts": 1754737716193173, "dur": 51549, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 8589934592, "ts": 1754737716244724, "dur": 1374, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737718762733, "dur": 2, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3100, "tid": 4294967296, "ts": 1754737715986699, "dur": 287720, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754737715989120, "dur": 113260, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754737716274536, "dur": 610430, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754737716885099, "dur": 1868634, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754737716895704, "dur": 109418, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754737718753745, "dur": 2692, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754737718755783, "dur": 26, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754737718756440, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737718762736, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754737717141196, "dur": 50, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737717141280, "dur": 99054, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737717240342, "dur": 278, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737717240719, "dur": 53, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754737717240772, "dur": 391, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737717242540, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_6E85C30F177A7F51.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737717243446, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737717241180, "dur": 18601, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737717259796, "dur": 1486389, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737718746186, "dur": 261, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737718746660, "dur": 60, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737718746744, "dur": 1288, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754737717241742, "dur": 18104, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717260122, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_A00BE41311F50426.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737717260238, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717260306, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754737717260407, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754737717260624, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754737717260707, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754737717260818, "dur": 208, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1754737717261685, "dur": 608, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754737717262294, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717262470, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717262633, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717262774, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717263087, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717263239, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717263383, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717264162, "dur": 5456, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\StateUnit.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754737717263804, "dur": 5891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717269695, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717270071, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717270592, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737717271179, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717271239, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737717271452, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737717271699, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737717271342, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754737717272025, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717272214, "dur": 587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717272801, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717273145, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754737717273467, "dur": 272399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717546134, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737717546511, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737717547571, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737717545886, "dur": 2053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.simple.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754737717547940, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717548348, "dur": 1297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.turn-based.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754737717549646, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737717550164, "dur": 400, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737717550754, "dur": 1408, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-sysinfo-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737717552233, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737717552380, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Metadata.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737717549765, "dur": 3525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754737717553349, "dur": 1192846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717242052, "dur": 17915, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717259976, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737717259972, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_93739C87338A598F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754737717260215, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1754737717260204, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_180837F1985FCEBE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754737717260567, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754737717260785, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754737717260893, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754737717261170, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754737717261501, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717262041, "dur": 358, "ph": "X", "name": "WriteResponseFile", "args": {"detail": "Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754737717262400, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717262570, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717262740, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717262909, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717263135, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717263278, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717263426, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717263816, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717264275, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717264432, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717264610, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717264905, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717265082, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717265305, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717265461, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717265668, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717265929, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717266156, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717266354, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717266507, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717266657, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717266815, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717266957, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717267096, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717267240, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717267390, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717267555, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717267716, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717267855, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717267994, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717268152, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717268308, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717268455, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717268599, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717268737, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717268877, "dur": 124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717269001, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717269373, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717269475, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717270083, "dur": 775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717271200, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Messaging\\ExceptionEventArgs.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754737717270859, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754737717271451, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754737717271616, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754737717272005, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717272166, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754737717272276, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754737717272637, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717272807, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717273158, "dur": 272698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717545858, "dur": 1752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754737717547655, "dur": 1317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754737717549582, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.CookiePolicy.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737717550163, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737717550620, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737717549000, "dur": 1702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754737717550755, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\URPWizard.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737717551913, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737717552193, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737717552333, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737717552495, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737717550754, "dur": 1871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/URPWizard.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754737717552783, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Cinemachine.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754737717552893, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737717553029, "dur": 1180246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737718733277, "dur": 11907, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737718733277, "dur": 11909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737718745207, "dur": 917, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737717242038, "dur": 17918, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717260118, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_33F6F62BD0C52DED.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737717260214, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717260444, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754737717260531, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737717260595, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737717261469, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737717261853, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737717260662, "dur": 2068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737717262773, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717263079, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717263215, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717263353, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717263794, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717264431, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717264580, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717264771, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717264919, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717265076, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717265299, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717265450, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717265617, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717265927, "dur": 1661, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics\\OnCollisionEnter.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754737717265793, "dur": 1882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717267676, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717267821, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717267956, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717268097, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717268237, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717268388, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717268524, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717268680, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717268823, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717268975, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717269149, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717269529, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717270078, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717270862, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737717271355, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737717271555, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737717271840, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717272015, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717272201, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737717272319, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737717272605, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717272806, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717273143, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737717273349, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717273417, "dur": 272436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717546368, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737717545856, "dur": 1828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.docs.getting_started.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754737717547684, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717548140, "dur": 520, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737717547794, "dur": 1914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754737717549708, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717549902, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737717550754, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Web.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737717549826, "dur": 1777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754737717551604, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737717551854, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737717552021, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737717552194, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737717551757, "dur": 1588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754737717553390, "dur": 1192823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717242152, "dur": 17834, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717260043, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737717260221, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717260407, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754737717260500, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754737717260597, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754737717260740, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754737717260865, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754737717261002, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754737717262415, "dur": 595, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/GlassSystem.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754737717263011, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717263163, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717263306, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717263848, "dur": 971, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Plugin\\Changelogs\\Changelog_1_1_3.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754737717263848, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717264968, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717265142, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717265757, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717266055, "dur": 3029, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Control\\SwitchOnString.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754737717265963, "dur": 3606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717269570, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717270113, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717270598, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737717270667, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717270856, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737717270966, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737717270746, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754737717271403, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717271632, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717271716, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737717271790, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717272063, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754737717272404, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717272617, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717272802, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717273159, "dur": 272722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717545882, "dur": 1564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737717547447, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717548097, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-heap-l1-1-0.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737717547649, "dur": 1326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737717550164, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737717549003, "dur": 1447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737717550961, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-libraryloader-l1-1-0.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737717550490, "dur": 1373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737717551908, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717552492, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717552566, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737717552711, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717552892, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737717552970, "dur": 920630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737718473603, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754737718473602, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754737718473736, "dur": 888, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754737718474628, "dur": 271578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717241697, "dur": 18121, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717259996, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_F8B5F041D001A362.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737717260764, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754737717261471, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717261680, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717261855, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717262018, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717262161, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717262500, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717262941, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717263181, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717263872, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\AssetPostProcessors\\ShaderGraphMaterialsUpdater.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754737717263705, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717264503, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717264723, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717264925, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717265099, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717265302, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717265454, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717265608, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717265768, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717265994, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717266163, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717266337, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717266491, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717266641, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717266778, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717266929, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717267159, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717267304, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717267458, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717267631, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717267814, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717267984, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717268128, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717268273, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717268427, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717268576, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717268719, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717268860, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717269011, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717269560, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717270085, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717270512, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737717270577, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717270936, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737717271255, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737717270869, "dur": 1251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754737717272196, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737717272308, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754737717272799, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737717272887, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754737717273085, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717273156, "dur": 1984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717275141, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737717275223, "dur": 271169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717547572, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737717548097, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebProxy.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737717546395, "dur": 2294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754737717548743, "dur": 1319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754737717550063, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717550516, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737717550121, "dur": 2003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754737717552124, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717552375, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754737717552510, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717552563, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/CrashKonijn.Goap.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1754737717552898, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737717553213, "dur": 1192979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717241728, "dur": 18107, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717260042, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_5F9E4D3CAA1E8246.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737717260204, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717260277, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737717260276, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737717260370, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754737717260577, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754737717260822, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754737717260930, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754737717261112, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754737717262648, "dur": 374, "ph": "X", "name": "WriteResponseFile", "args": {"detail": "Library/Bee/artifacts/rsp/5209273002131145429.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754737717263023, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717263175, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717263329, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717263475, "dur": 716, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.State\\Description\\StateMacroDescriptor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754737717263475, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717264465, "dur": 3060, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Connections\\UnitConnectionWidget.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754737717264346, "dur": 3297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717267644, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717267813, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717268002, "dur": 1516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Utilities\\Observables\\Observer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754737717267957, "dur": 1894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717269851, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717270127, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717270600, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737717270978, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737717271142, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737717271453, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737717271141, "dur": 1202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754737717272343, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717272570, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737717272656, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754737717272856, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717273146, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717273778, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737717273876, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754737717274117, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717274183, "dur": 273294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717548098, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cryptography.KeyDerivation.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737717547480, "dur": 1478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754737717548959, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717549195, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737717549133, "dur": 1507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754737717551913, "dur": 228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737717550679, "dur": 1506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754737717552237, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737717552236, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737717552550, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754737717552834, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Tests.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754737717552908, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737717553353, "dur": 1192836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717241766, "dur": 18095, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717260024, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737717260219, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737717260218, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737717260356, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754737717260635, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754737717260713, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754737717260991, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754737717261121, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754737717261495, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717261677, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717261839, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717261989, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717262134, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717262296, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717262449, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717263027, "dur": 650, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.crashkonijn.goap@601802d6e1\\Runtime\\CrashKonijn.Agent.Runtime\\ActionRunner.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754737717262609, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717263852, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\EditablePath\\ISnapping.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754737717263785, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717264443, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717264597, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717264776, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717264931, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717265153, "dur": 3958, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\GradientInputMaterialSlot.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754737717265086, "dur": 4155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717269242, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717269317, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717269413, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717270140, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717270694, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737717270816, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737717270938, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737717271035, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737717271343, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717271467, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737717271578, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737717271908, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717271976, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717272203, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717272798, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737717272881, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737717273332, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737717273437, "dur": 1052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737717274581, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737717275028, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737717275316, "dur": 270632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717545973, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737717547513, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737717545950, "dur": 1987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754737717548140, "dur": 479, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737717547976, "dur": 1914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754737717549891, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737717550006, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754737717551960, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processthreads-l1-1-0.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737717552135, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Hosting.Abstractions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737717552334, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737717551494, "dur": 1652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754737717553190, "dur": 1192997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717241795, "dur": 18078, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717259971, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_591A98B8662B29F2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737717260035, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_591A98B8662B29F2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737717260124, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_087D5123E2EA5605.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737717260210, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737717260209, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737717260314, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754737717260624, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754737717260783, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754737717261702, "dur": 751, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/GlassSystem.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754737717262454, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717262620, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717262849, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717263063, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717263178, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717263320, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717263568, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717263740, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717264225, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717264628, "dur": 4877, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\GraphEditorView.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754737717264567, "dur": 5053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717269620, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717270061, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717270647, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737717270901, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737717271452, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737717271699, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737717271326, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737717271990, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737717272085, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737717272713, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717272861, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717273155, "dur": 1811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717275035, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737717275229, "dur": 270649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717547289, "dur": 293, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737717545880, "dur": 1859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754737717547740, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717549200, "dur": 975, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737717550205, "dur": 605, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737717547822, "dur": 3217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754737717551040, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737717551913, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Xml.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737717552123, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737717552232, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737717552381, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737717552661, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737717551106, "dur": 1901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754737717553051, "dur": 1193130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717241827, "dur": 18060, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717260045, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737717260154, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_1B8FAEC3513ADBA8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737717260263, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737717260263, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737717260746, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754737717261691, "dur": 570, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754737717262262, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717262422, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717262590, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717262814, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717263005, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717263177, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717263324, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717263518, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717263677, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717263834, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717264291, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717264460, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717264654, "dur": 4429, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Colors\\NoColors.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754737717264614, "dur": 5036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717269651, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717270081, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717270584, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1754737717270517, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737717270715, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737717270856, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737717271199, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737717270830, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754737717271412, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717271747, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737717271933, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717272166, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717272223, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717272800, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717273144, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754737717273424, "dur": 272446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717545888, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737717546473, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737717547294, "dur": 315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737717545872, "dur": 2001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754737717548097, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737717549196, "dur": 996, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737717550233, "dur": 1729, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737717551963, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737717552334, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737717547899, "dur": 4510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754737717552550, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737717552676, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717552896, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737717553054, "dur": 1193121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717241851, "dur": 18046, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717260206, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717260396, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754737717260613, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754737717260843, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754737717261023, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754737717261484, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717261689, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717261840, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717261984, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717262125, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717262308, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717262477, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717262652, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717263122, "dur": 760, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.vscode@1.2.5\\Editor\\VSCodeDiscovery.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754737717263882, "dur": 789, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.vscode@1.2.5\\Editor\\ProjectGeneration\\StringUtils.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754737717263100, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717264779, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717264948, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717265112, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717265338, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717265490, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717265659, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717265816, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717266005, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717266179, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717266349, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717266495, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717266641, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717266787, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717266948, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717267089, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717267244, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717267397, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717267688, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717267834, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717267989, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717268134, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717268276, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717268424, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717268591, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717268735, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717268929, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717269030, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717269188, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717269463, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717270113, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717270515, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737717270683, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737717271140, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.crashkonijn.goap@601802d6e1\\Runtime\\CrashKonijn.Agent.Core\\Interfaces\\IActionContext.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754737717271421, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737717270599, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754737717271477, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717271586, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754737717272106, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717272165, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737717272282, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754737717272581, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717272799, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717273147, "dur": 1420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717274633, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754737717274842, "dur": 271056, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717545900, "dur": 1365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754737717547266, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717547353, "dur": 1297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754737717548651, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717550164, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737717548732, "dur": 1488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754737717550220, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737717551248, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737717552333, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737717552661, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737717550486, "dur": 2509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754737717553031, "dur": 1193147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717241902, "dur": 18006, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717260153, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_706C4E83917515AE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737717260254, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737717260254, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737717260437, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754737717260500, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754737717260864, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754737717261701, "dur": 1138, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/GlassSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754737717262839, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717263026, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717263165, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717263315, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717263473, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717263679, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717263851, "dur": 1314, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Ports\\ValueOutputWidget.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754737717265165, "dur": 782, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Ports\\ValueInputWidget.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754737717263826, "dur": 2245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717266071, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717266226, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717266379, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717266529, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717266680, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717266838, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717266987, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717267127, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717267282, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717267500, "dur": 1825, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Collections\\VariantCollection.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754737717267426, "dur": 2103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717269530, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717270109, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717270690, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737717270774, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737717270887, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754737717271194, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717271377, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_9468B054363B0720.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737717271514, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717271612, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717271982, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717272207, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717272799, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717273182, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737717273275, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754737717274159, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737717274227, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754737717274562, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737717274629, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754737717274948, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754737717275136, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737717275206, "dur": 270643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717546420, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737717545852, "dur": 1945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Resolver.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754737717547798, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717547915, "dur": 1270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754737717549185, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717550164, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737717550232, "dur": 533, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737717549246, "dur": 1853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754737717552194, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737717552334, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737717551134, "dur": 1577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754737717552824, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.crashkonijn.docs.getting_started.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1754737717552914, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737717553396, "dur": 1192807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717241944, "dur": 17976, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717259991, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737717259990, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_022E81689B98A39E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737717260228, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717260323, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754737717260499, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754737717260690, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754737717260848, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754737717261686, "dur": 392, "ph": "X", "name": "WriteResponseFile", "args": {"detail": "Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754737717262079, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717262222, "dur": 862, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@2.10.1\\Runtime\\Impulse\\CinemachineCollisionImpulseSource.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754737717263111, "dur": 1411, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@2.10.1\\Runtime\\Helpers\\CinemachineInputProvider.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754737717262222, "dur": 2447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717264669, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717265247, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717265611, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717265835, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717266110, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717266469, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717266615, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717266761, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717267010, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717267172, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717267324, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717267532, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717267795, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717267939, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717268076, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717268238, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717268403, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717268539, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717268678, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717268820, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717269002, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717269176, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717269424, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717270062, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717270590, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737717270729, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737717271139, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737717271013, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754737717271646, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717271737, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737717271829, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717272006, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717272198, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737717272314, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754737717272608, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717272801, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737717272893, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754737717273191, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717273266, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737717273355, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754737717273826, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754737717274070, "dur": 271789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717546123, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737717545861, "dur": 1478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754737717547571, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737717547771, "dur": 336, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737717548139, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737717549244, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737717549393, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737717547363, "dur": 2124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754737717549488, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717550916, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737717549621, "dur": 2227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754737717552489, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717552574, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754737717552810, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737717552861, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754737717552926, "dur": 571387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737718124315, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\GlassSystem.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754737718124314, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/GlassSystem.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754737718124440, "dur": 1753, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/GlassSystem.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754737718126196, "dur": 619995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717241973, "dur": 17957, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717260113, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_AB4BD236749C82D4.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737717260246, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737717260245, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737717260328, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737717260479, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754737717260718, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754737717262116, "dur": 617, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754737717262734, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717262986, "dur": 881, "ph": "X", "name": "File", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Cors.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737717262913, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717264240, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717264393, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717264540, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717264887, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717265085, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717265320, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717266203, "dur": 705, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector2\\DeprecatedVector2Add.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754737717265636, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717266909, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717267280, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717267439, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717267642, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717267822, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717267958, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717268115, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717268261, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717268410, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717268541, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717268689, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717268828, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717268966, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717269131, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717269316, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717269416, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717270067, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717270538, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737717270639, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737717270915, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717271003, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717271243, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\GlassSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754737717271152, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737717271525, "dur": 88, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737717271625, "dur": 303159, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737717575361, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\GlassSystem.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754737717574797, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737717575508, "dur": 256818, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737717833796, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\GlassSystem.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737717833590, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737717834231, "dur": 334124, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737718170013, "dur": 2347, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737718172413, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754737718169749, "dur": 2732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737718172817, "dur": 296441, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737718470629, "dur": 15357, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737718486945, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\rsp\\2781082588993088484.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754737718470627, "dur": 16453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754737718487859, "dur": 153, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737718488030, "dur": 240076, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754737718733269, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1754737718733268, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1754737718733377, "dur": 983, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1754737718734363, "dur": 11817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717242000, "dur": 17941, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717259971, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717260085, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_09D119D0E0AA1ABF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737717260206, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737717260205, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_55F435D8463A793C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737717260453, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754737717260595, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754737717260741, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754737717261474, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717261676, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717261835, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717261974, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717262143, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717262296, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717262743, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717262907, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717263147, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717263295, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717263525, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717263781, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717264434, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717264785, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717265005, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717265218, "dur": 2776, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\ShaderUtils.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754737717265152, "dur": 2986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717268139, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717268293, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717268450, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717268607, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717268746, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717268940, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717269408, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717270099, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717270514, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737717270856, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737717270749, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737717271450, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737717271717, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737717272157, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737717272270, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737717272663, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737717273153, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737717273548, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737717273847, "dur": 272017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717545865, "dur": 1383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737717547571, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737717547772, "dur": 337, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737717548587, "dur": 337, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737717547305, "dur": 2171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737717550232, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipes.AccessControl.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737717549502, "dur": 1396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737717551913, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737717552194, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737717550923, "dur": 1770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737717552694, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737717552794, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754737717552947, "dur": 616808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737718169758, "dur": 23393, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737718194121, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\rsp\\10678863128556690338.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754737718169757, "dur": 24479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737718195074, "dur": 121, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737718195210, "dur": 271981, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737718473593, "dur": 23118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737718473591, "dur": 23123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737718496751, "dur": 754, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737718497508, "dur": 248693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717241679, "dur": 18128, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717259815, "dur": 3072, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717262945, "dur": 917, "ph": "X", "name": "File", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737717262887, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717264450, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717264626, "dur": 1316, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Inspector\\PropertyDrawers\\DropdownPropertyDrawer.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754737717264602, "dur": 1538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717266140, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717266332, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717266480, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717266624, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717266762, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717266917, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717267059, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717267206, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717267356, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717267502, "dur": 1729, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Volume\\KeyframeUtility.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754737717267502, "dur": 2007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717269510, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717270079, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717270518, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737717270682, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737717270650, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737717271115, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717271467, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717271719, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737717271928, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717272197, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737717272291, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717272378, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737717273175, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737717273287, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737717274109, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737717274187, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737717274485, "dur": 271377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717545890, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737717546034, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737717547294, "dur": 332, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737717547742, "dur": 363, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737717545866, "dur": 2260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754737717548127, "dur": 1283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717549415, "dur": 1315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754737717550731, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717551960, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737717552470, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737717550789, "dur": 1978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754737717552915, "dur": 280682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717833600, "dur": 8885, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\GlassSystem.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737717843410, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\rsp\\5209273002131145429.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754737717833598, "dur": 9885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/GlassSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754737717843487, "dur": 112, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737717843619, "dur": 275100, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/GlassSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754737718124308, "dur": 12578, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\GlassSystem.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737718124307, "dur": 12582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/GlassSystem.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737718136913, "dur": 812, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/GlassSystem.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737718137729, "dur": 608471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737717242077, "dur": 17900, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737717259982, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737717260212, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737717260211, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737717260308, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737717260530, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737717260598, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737717261471, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737717262778, "dur": 1571, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\PlayerTestAssemblyProvider.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754737717264397, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\FloatEqualityComparer.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754737717260722, "dur": 4045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754737717264848, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737717265142, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737717265221, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737717265893, "dur": 3717, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737717264966, "dur": 4986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754737717270102, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737717270189, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754737717270516, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737717270586, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737717270585, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754737717271073, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737717271177, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737717271376, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737717271350, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754737717271771, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737717271863, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 16, "ts": 1754737717272340, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737717272400, "dur": 115, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737717272525, "dur": 271341, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 16, "ts": 1754737717545859, "dur": 1389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.complex.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737717547310, "dur": 1254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737717548615, "dur": 1249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737717549865, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737717550270, "dur": 1173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737717551478, "dur": 1427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737717552955, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737717553041, "dur": 1193142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737718751837, "dur": 1584, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1754737716570192, "dur": 295202, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754737716570941, "dur": 47768, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754737716813101, "dur": 3787, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754737716816890, "dur": 48490, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754737716817881, "dur": 29772, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754737716870156, "dur": 843, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754737716869798, "dur": 1380, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754737716243258, "dur": 1755, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737716245027, "dur": 763, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737716245902, "dur": 52, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754737716245954, "dur": 346, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737716247001, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3C3F89D87E20F844.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737716248547, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737716267348, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754737716246316, "dur": 21311, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737716267640, "dur": 293, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737716267934, "dur": 90, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737716268048, "dur": 192, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737716268474, "dur": 1082, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754737716246548, "dur": 21104, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737716267658, "dur": 266, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737716246649, "dur": 21063, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737716267838, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737716246579, "dur": 21083, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737716267836, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737716267922, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_41DA9226DA4902FE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737716246634, "dur": 21058, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737716267700, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A3CA169B49A7C799.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737716267857, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_BE454E773931EEAC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737716267919, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_BE454E773931EEAC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737716246610, "dur": 21069, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737716267838, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737716246656, "dur": 21078, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737716267751, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_539666F57CEC7FE9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737716267850, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_A8B81DEC69E8E1CF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737716246705, "dur": 21072, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737716267840, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737716246680, "dur": 21078, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737716267776, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_1E012B284EB8934B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737716267837, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737716246731, "dur": 21059, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737716267834, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_F4EF6CA575FE5075.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737716246762, "dur": 21044, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737716267821, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_504B268BBB0FC6D0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737716246799, "dur": 21023, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737716267833, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737716267828, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_D2B66E0C49661945.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737716246825, "dur": 21010, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737716267856, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737716267846, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_ED51E50A679089CA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737716267924, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_ED51E50A679089CA.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737716246862, "dur": 21000, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737716267880, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_188EDD6374EAFAAD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737716267966, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_188EDD6374EAFAAD.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737716246902, "dur": 20977, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737716246926, "dur": 20964, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737716246953, "dur": 20952, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737716272648, "dur": 241, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3100, "tid": 24783, "ts": 1754737718763069, "dur": 1734, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 3100, "tid": 24783, "ts": 1754737718766427, "dur": 30, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 3100, "tid": 24783, "ts": 1754737718766614, "dur": 12, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3100, "tid": 24783, "ts": 1754737718764894, "dur": 1530, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737718766507, "dur": 107, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737718766656, "dur": 105, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737718760266, "dur": 6959, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}