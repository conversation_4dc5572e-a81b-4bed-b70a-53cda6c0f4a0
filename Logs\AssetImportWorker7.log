Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.38f1c1 (b17906c7b2b6) revision 11630854'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 48295 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\GameSoft\Unity\Editor\2022.3.38f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker7
-projectPath
D:/GameSoft/Unity/g-pro/goap
-logFile
Logs/AssetImportWorker7.log
-srvPort
57450
Successfully changed project path to: D:/GameSoft/Unity/g-pro/goap
D:/GameSoft/Unity/g-pro/goap
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [26500]  Target information:

Player connection [26500]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3135149437 [EditorId] 3135149437 [Version] 1048832 [Id] WindowsEditor(7,Top1a-Va11hallA) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [26500] Host joined multi-casting on [***********:54997]...
Player connection [26500] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
Refreshing native plugins compatible for Editor in 20.63 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.38f1c1 (b17906c7b2b6)
[Subsystems] Discovering subsystems at path D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/GameSoft/Unity/g-pro/goap/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 5070 Ti (ID=0x2c05)
    Vendor:   NVIDIA
    VRAM:     15907 MB
    Driver:   32.0.15.7688
Initialize mono
Mono path[0] = 'D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56168
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005649 seconds.
- Loaded All Assemblies, in  0.227 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.173 seconds
Domain Reload Profiling: 399ms
	BeginReloadAssembly (68ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (5ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (91ms)
		LoadAssemblies (66ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (89ms)
			TypeCache.Refresh (88ms)
				TypeCache.ScanAssembly (79ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (174ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (136ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (88ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.500 seconds
Refreshing native plugins compatible for Editor in 4.66 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.533 seconds
Domain Reload Profiling: 1029ms
	BeginReloadAssembly (99ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (21ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (336ms)
		LoadAssemblies (219ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (171ms)
			TypeCache.Refresh (141ms)
				TypeCache.ScanAssembly (130ms)
			ScanForSourceGeneratedMonoScriptInfo (20ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (533ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (413ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (56ms)
			ProcessInitializeOnLoadAttributes (262ms)
			ProcessInitializeOnLoadMethodAttributes (84ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.32 seconds
Refreshing native plugins compatible for Editor in 23.75 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6202 Unused Serialized files (Serialized files now loaded: 0)
Unloading 93 unused Assets / (426.5 KB). Loaded Objects now: 6662.
Memory consumption went from 234.0 MB to 233.6 MB.
Total: 11.741100 ms (FindLiveObjects: 1.047900 ms CreateObjectMapping: 0.472900 ms MarkObjects: 9.811200 ms  DeleteObjects: 0.407200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/RecursiveBreakingDemo.cs: 29e1f786aa84bd8a28e7be6889b609cb -> 
  custom:scripting/monoscript/fileName/EnhancedGlassPanel.cs: b8a66b0a8808a006c11e87aaeeee65aa -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/DemoSceneSetup.cs: 27384b61f9a861e8c1dc4a180c4bd280 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/DemoSceneManager.cs: db20cf64161b5eaceaf37d01c3ba4fc4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/CompleteDemoSetup.cs: 5f5f9cb466bd7b2d3e54e39032114de7 -> 
  custom:scripting/monoscript/fileName/DemoUIManager.cs: cb3f9d74c3dfd14e09c51bd9f26a5151 -> 
  custom:scripting/monoscript/fileName/ImprovedFlyCamera.cs: 0d52ec8efe68e211158a0d220880d230 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:scripting/monoscript/fileName/RecursiveShard.cs: 2b66a6039c0528d6ed5cc616eebd3bfe -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/SimpleRecursiveShard.cs: 57ca122cce49ba32e478e52ad0b65e27 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:scripting/monoscript/fileName/RecursiveGlassPanel.cs: e2753ab148a005cc281791079e9f7cd6 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/URPMaterialCreator.cs: 7facad6d43bcfb6e5492f16722b181b6 -> 
  custom:scripting/monoscript/fileName/EnhancedShard.cs: 58e0ddde407ee99b738f172bd42b317e -> 
  custom:scripting/monoscript/fileName/SimpleRecursiveGlass.cs: 125a1491ed4282b42945314005a23b6f -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/URPSetupChecker.cs: dfd097f6080837650b8a2beeb65a1b92 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GlassPrefabGenerator.cs: 15a8a66f3178af92fb37327aecf95381 -> 
  custom:scripting/monoscript/fileName/SimpleFlyCamera.cs: e78fe1d0e0159e07fdb1123a605a4410 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
TcpMessagingSession - receive error: operation aborted. errorcode: 995, details: 由于线程退出或应用程序请求，已中止 I/O 操作。
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0