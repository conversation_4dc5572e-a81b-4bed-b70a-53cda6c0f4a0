%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &111905991
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 111905995}
  - component: {fileID: 111905994}
  - component: {fileID: 111905993}
  - component: {fileID: 111905992}
  m_Layer: 0
  m_Name: r (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &111905992
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 111905991}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1.1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &111905993
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 111905991}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1bb8d9be66ba1a84aa62fffc5126fa8c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &111905994
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 111905991}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &111905995
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 111905991}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.39936996, y: -4.2174845, z: -12.267372}
  m_LocalScale: {x: 20, y: 0.2, z: 4}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 780728714}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &207591897
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 207591901}
  - component: {fileID: 207591900}
  - component: {fileID: 207591899}
  - component: {fileID: 207591898}
  m_Layer: 0
  m_Name: r (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &207591898
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 207591897}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &207591899
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 207591897}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1bb8d9be66ba1a84aa62fffc5126fa8c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &207591900
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 207591897}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &207591901
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 207591897}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.3226302, y: -1.2174845, z: -13.756372}
  m_LocalScale: {x: 0.4409128, y: 6.572524, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 780728714}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &245470023
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 245470027}
  - component: {fileID: 245470026}
  - component: {fileID: 245470025}
  - component: {fileID: 245470024}
  m_Layer: 0
  m_Name: Capsule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &245470024
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 245470023}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &245470025
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 245470023}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &245470026
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 245470023}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &245470027
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 245470023}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 2.9699998, y: 1.12, z: -0.109999895}
  m_LocalScale: {x: 0.7, y: 1, z: 0.7}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &253512364
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 253512366}
  - component: {fileID: 253512365}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &253512365
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 253512364}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 1
  m_Shape: 0
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!4 &253512366
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 253512364}
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 20
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!1 &256780139
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 256780143}
  - component: {fileID: 256780142}
  - component: {fileID: 256780141}
  - component: {fileID: 256780140}
  m_Layer: 0
  m_Name: r (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &256780140
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256780139}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &256780141
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256780139}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1bb8d9be66ba1a84aa62fffc5126fa8c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &256780142
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256780139}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &256780143
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256780139}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 4.8793697, y: -1.2174845, z: -13.756372}
  m_LocalScale: {x: 0.4409128, y: 6.572524, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 780728714}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &301275797
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 301275800}
  - component: {fileID: 301275799}
  - component: {fileID: 301275798}
  m_Layer: 1
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!102 &301275798
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 301275797}
  m_Text: UV
  m_OffsetZ: 0
  m_CharacterSize: 1
  m_LineSpacing: 1
  m_Anchor: 0
  m_Alignment: 0
  m_TabSize: 4
  m_FontSize: 64
  m_FontStyle: 0
  m_RichText: 1
  m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
  m_Color:
    serializedVersion: 2
    rgba: 4294967295
--- !u!23 &301275799
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 301275797}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &301275800
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 301275797}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -4.52, y: 5.646, z: -2.35}
  m_LocalScale: {x: 0.04, y: 0.04, z: 0.04}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 14
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &327073187
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 327073191}
  - component: {fileID: 327073190}
  - component: {fileID: 327073189}
  - component: {fileID: 327073188}
  m_Layer: 0
  m_Name: r (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &327073188
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 327073187}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &327073189
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 327073187}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1bb8d9be66ba1a84aa62fffc5126fa8c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &327073190
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 327073187}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &327073191
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 327073187}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 10.17937, y: -1.2174845, z: -12.267372}
  m_LocalScale: {x: 0.4409128, y: 6.572524, z: 4}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 780728714}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &615995076
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.69
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.14
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.35
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Name
      value: glass (1)
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Layer
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_TagString
      value: Glass
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6617583239465076362, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 868f1ed900aac314f91480651543f59c, type: 3}
--- !u!1 &615995077 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_PrefabInstance: {fileID: 615995076}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &615995078
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 615995077}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 02cd666ee03082f419820ba60f8b1362, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Patterns:
  - {fileID: 4300000, guid: 32f121cbf83f79f45b5fe375c81caa1a, type: 2}
  - {fileID: 4300000, guid: 2cb3fb1ab07b00c4ca94a5bb73261a5a, type: 2}
--- !u!1 &660210142
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 660210146}
  - component: {fileID: 660210145}
  - component: {fileID: 660210144}
  - component: {fileID: 660210143}
  m_Layer: 0
  m_Name: r (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &660210143
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 660210142}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &660210144
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 660210142}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1bb8d9be66ba1a84aa62fffc5126fa8c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &660210145
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 660210142}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &660210146
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 660210142}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -9.37063, y: -1.2174845, z: -12.267372}
  m_LocalScale: {x: 0.4409128, y: 6.572524, z: 4}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 780728714}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &747962303
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 747962304}
  - component: {fileID: 747962306}
  - component: {fileID: 747962305}
  m_Layer: 5
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &747962304
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 747962303}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1175523581}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 25, y: 25}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &747962305
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 747962303}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 77bb61a7ec35a904d9fbca2a2566572b, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: eaca59b1ffc69084982226f10cfb922b, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &747962306
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 747962303}
  m_CullTransparentMesh: 1
--- !u!1 &780728713
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 780728714}
  m_Layer: 0
  m_Name: Building
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &780728714
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 780728713}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.4606302, y: 4.2174845, z: 11.917373}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 256780143}
  - {fileID: 207591901}
  - {fileID: 1625205272}
  - {fileID: 111905995}
  - {fileID: 2119999200}
  - {fileID: 660210146}
  - {fileID: 327073191}
  m_Father: {fileID: 0}
  m_RootOrder: 26
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &907859501
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_RootOrder
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.012563
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.5900002
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 4.31
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.35
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Name
      value: glass (9)
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Layer
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_TagString
      value: Glass
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6617583239465076362, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 868f1ed900aac314f91480651543f59c, type: 3}
--- !u!1 &907859502 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_PrefabInstance: {fileID: 907859501}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &907859503
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 907859502}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 02cd666ee03082f419820ba60f8b1362, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Patterns:
  - {fileID: 4300000, guid: 32f121cbf83f79f45b5fe375c81caa1a, type: 2}
  - {fileID: 4300000, guid: 2cb3fb1ab07b00c4ca94a5bb73261a5a, type: 2}
--- !u!1001 &921760204
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_RootOrder
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalScale.z
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.x
      value: -2.19
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 4.31
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.35
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Name
      value: glass (8)
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Layer
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_TagString
      value: Glass
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6617583239465076362, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 868f1ed900aac314f91480651543f59c, type: 3}
--- !u!1 &921760205 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_PrefabInstance: {fileID: 921760204}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &921760206
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 921760205}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 02cd666ee03082f419820ba60f8b1362, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Patterns:
  - {fileID: 4300000, guid: 32f121cbf83f79f45b5fe375c81caa1a, type: 2}
  - {fileID: 4300000, guid: 2cb3fb1ab07b00c4ca94a5bb73261a5a, type: 2}
--- !u!1001 &921942952
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1100597266}
    m_Modifications:
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalScale.y
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalScale.z
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.y
      value: -10.399998
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Name
      value: glass (5)
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_TagString
      value: Glass
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6617583239465076362, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 868f1ed900aac314f91480651543f59c, type: 3}
--- !u!1 &921942953 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_PrefabInstance: {fileID: 921942952}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &925532285
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 925532287}
  - component: {fileID: 925532286}
  m_Layer: 0
  m_Name: Reflection Probe
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!215 &925532286
ReflectionProbe:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 925532285}
  m_Enabled: 1
  serializedVersion: 2
  m_Type: 0
  m_Mode: 1
  m_RefreshMode: 0
  m_TimeSlicingMode: 0
  m_Resolution: 128
  m_UpdateFrequency: 0
  m_BoxSize: {x: 18.900667, y: 7.49, z: 4.6}
  m_BoxOffset: {x: 2.8753347, y: 0, z: 0}
  m_NearClip: 0.3
  m_FarClip: 1000
  m_ShadowDistance: 100
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 53
  m_IntensityMultiplier: 1
  m_BlendDistance: 1
  m_HDR: 1
  m_BoxProjection: 0
  m_RenderDynamicObjects: 0
  m_UseOcclusionCulling: 1
  m_Importance: 1
  m_CustomBakedTexture: {fileID: 0}
--- !u!4 &925532287
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 925532285}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.55999994, y: 2.47, z: -0.65999985}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &993412198
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 993412204}
  - component: {fileID: 993412203}
  - component: {fileID: 993412202}
  - component: {fileID: 993412201}
  - component: {fileID: 993412200}
  - component: {fileID: 993412199}
  - component: {fileID: 993412205}
  - component: {fileID: 993412206}
  m_Layer: 0
  m_Name: JointAnchor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!145 &993412199
SpringJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 993412198}
  m_ConnectedBody: {fileID: 1100597262}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0.99999994, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  serializedVersion: 2
  m_Spring: 10
  m_Damper: 0.2
  m_MinDistance: 0
  m_MaxDistance: 0
  m_Tolerance: 0.025
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!54 &993412200
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 993412198}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &993412201
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 993412198}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &993412202
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 993412198}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &993412203
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 993412198}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &993412204
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 993412198}
  m_LocalRotation: {x: -0, y: -0, z: -0.7071066, w: 0.707107}
  m_LocalPosition: {x: -6.5400014, y: 6.1, z: -2.35}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: -90}
--- !u!120 &993412205
LineRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 993412198}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 26329d372a4076942909cd27b9f2f156, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions:
  - {x: -6.5400014, y: 6.1, z: -2.35}
  - {x: -6.5399995, y: 5.35, z: -2.35}
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 1
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0.046669006
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 0, g: 0, b: 0, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 2
      m_NumAlphaKeys: 2
    numCornerVertices: 0
    numCapVertices: 0
    alignment: 0
    textureMode: 0
    shadowBias: 0.5
    generateLightingData: 0
  m_UseWorldSpace: 1
  m_Loop: 0
--- !u!114 &993412206
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 993412198}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2adce70f5f51afa46a04c774e63d221b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _targetTransform: {fileID: 1100597266}
  _targetOffset: {x: 0, y: 0, z: 0}
--- !u!1001 &1085733347
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_RootOrder
      value: 9
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.9
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.9
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.54
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.25
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7511558181221131132, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: eb9e45c94d987df47b567be43c866f5f, type: 2}
    - target: {fileID: -7511558181221131132, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_Materials.Array.data[1]
      value: 
      objectReference: {fileID: 2100000, guid: 2f1e6a21bd5124946884e42a6d95dc10, type: 2}
    - target: {fileID: 919132149155446097, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_Name
      value: glassHexa
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_Layer
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
      propertyPath: m_TagString
      value: Glass
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
--- !u!1 &1085733348 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 8adaaa62e55950742b54f844d06ede31, type: 3}
  m_PrefabInstance: {fileID: 1085733347}
  m_PrefabAsset: {fileID: 0}
--- !u!64 &1085733353
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1085733348}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: -9146618070568788744, guid: 5043f9f2c00a89844945b27f20fe808c, type: 3}
--- !u!114 &1085733354
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1085733348}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 02cd666ee03082f419820ba60f8b1362, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Patterns:
  - {fileID: 4300000, guid: 32f121cbf83f79f45b5fe375c81caa1a, type: 2}
  - {fileID: 4300000, guid: 2cb3fb1ab07b00c4ca94a5bb73261a5a, type: 2}
--- !u!1 &1100597261
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1100597266}
  - component: {fileID: 1100597265}
  - component: {fileID: 1100597264}
  - component: {fileID: 1100597263}
  - component: {fileID: 1100597262}
  - component: {fileID: 1100597267}
  m_Layer: 0
  m_Name: GlassHolder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!54 &1100597262
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1100597261}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 98
  m_CollisionDetection: 0
--- !u!65 &1100597263
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1100597261}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1100597264
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1100597261}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1100597265
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1100597261}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1100597266
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1100597261}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -6.5399995, y: 5.35, z: -2.35}
  m_LocalScale: {x: 2, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1361532667}
  m_Father: {fileID: 0}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1100597267
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1100597261}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a6ad67140d436424b845d198a9bc2dce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Force: {x: 0, y: 0, z: 0.3}
  Frequency: 0.2
--- !u!1 &1119110014
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1119110017}
  - component: {fileID: 1119110016}
  - component: {fileID: 1119110015}
  m_Layer: 1
  m_Name: Text (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!102 &1119110015
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1119110014}
  m_Text: Thick
  m_OffsetZ: 0
  m_CharacterSize: 1
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 0
  m_TabSize: 4
  m_FontSize: 64
  m_FontStyle: 0
  m_RichText: 1
  m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
  m_Color:
    serializedVersion: 2
    rgba: 4294967295
--- !u!23 &1119110016
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1119110014}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &1119110017
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1119110014}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.16, y: 5.51, z: -2.35}
  m_LocalScale: {x: 0.04, y: 0.04, z: 0.04}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 15
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1131436805
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.x
      value: -2.19
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.14
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.35
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Name
      value: glass (3)
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Layer
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_TagString
      value: Glass
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6617583239465076362, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 868f1ed900aac314f91480651543f59c, type: 3}
--- !u!1 &1131436806 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_PrefabInstance: {fileID: 1131436805}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1131436807
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1131436806}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 02cd666ee03082f419820ba60f8b1362, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Patterns:
  - {fileID: 4300000, guid: 32f121cbf83f79f45b5fe375c81caa1a, type: 2}
  - {fileID: 4300000, guid: 2cb3fb1ab07b00c4ca94a5bb73261a5a, type: 2}
--- !u!1 &1175523577
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1175523581}
  - component: {fileID: 1175523580}
  - component: {fileID: 1175523579}
  - component: {fileID: 1175523578}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1175523578
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1175523577}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1175523579
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1175523577}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1175523580
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1175523577}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &1175523581
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1175523577}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 747962304}
  m_Father: {fileID: 0}
  m_RootOrder: 23
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1001 &1259125024
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_RootOrder
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.x
      value: -4.35
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 4.31
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.35
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.000000400395
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70710677
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.000000400395
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -180
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Name
      value: glass (11)
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Layer
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_TagString
      value: Glass
      objectReference: {fileID: 0}
    - target: {fileID: 1388416426730809451, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 28467cead377cac4e9ade2434b85ed09, type: 2}
    m_RemovedComponents:
    - {fileID: 6617583239465076362, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 868f1ed900aac314f91480651543f59c, type: 3}
--- !u!1 &1259125025 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_PrefabInstance: {fileID: 1259125024}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1259125026
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1259125025}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 02cd666ee03082f419820ba60f8b1362, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Patterns:
  - {fileID: 4300000, guid: 32f121cbf83f79f45b5fe375c81caa1a, type: 2}
  - {fileID: 4300000, guid: 2cb3fb1ab07b00c4ca94a5bb73261a5a, type: 2}
--- !u!4 &1361532667 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_PrefabInstance: {fileID: 921942952}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1361532668
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 921942953}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 02cd666ee03082f419820ba60f8b1362, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Patterns:
  - {fileID: 4300000, guid: 32f121cbf83f79f45b5fe375c81caa1a, type: 2}
  - {fileID: 4300000, guid: 2cb3fb1ab07b00c4ca94a5bb73261a5a, type: 2}
--- !u!1 &1444087594
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1444087597}
  - component: {fileID: 1444087596}
  - component: {fileID: 1444087595}
  m_Layer: 1
  m_Name: Text (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!102 &1444087595
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1444087594}
  m_Text: inherited velocity
  m_OffsetZ: 0
  m_CharacterSize: 1
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 0
  m_TabSize: 4
  m_FontSize: 64
  m_FontStyle: 0
  m_RichText: 1
  m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
  m_Color:
    serializedVersion: 2
    rgba: 4294967295
--- !u!23 &1444087596
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1444087594}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &1444087597
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1444087594}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -6.56, y: 5.483, z: -2.35}
  m_LocalScale: {x: 0.04, y: 0.04, z: 0.04}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 17
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1487175697
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1487175701}
  - component: {fileID: 1487175700}
  - component: {fileID: 1487175699}
  - component: {fileID: 1487175698}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &1487175698
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1487175697}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!23 &1487175699
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1487175697}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: ca0eeeb41769fdb42aba190e7d685859, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1487175700
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1487175697}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1487175701
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1487175697}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.54, y: 7.45, z: -30.48}
  m_LocalScale: {x: 5.627504, y: 7.6528296, z: 5.627504}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 25
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1488223894
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalScale.x
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalScale.y
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.x
      value: 8.87
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 3.14
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.35
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Name
      value: glass (2)
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Layer
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_TagString
      value: Glass
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6617583239465076362, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 868f1ed900aac314f91480651543f59c, type: 3}
--- !u!1 &1488223895 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_PrefabInstance: {fileID: 1488223894}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1488223896
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1488223895}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 02cd666ee03082f419820ba60f8b1362, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Patterns:
  - {fileID: 4300000, guid: 32f121cbf83f79f45b5fe375c81caa1a, type: 2}
  - {fileID: 4300000, guid: 2cb3fb1ab07b00c4ca94a5bb73261a5a, type: 2}
--- !u!1 &1497258556
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1497258559}
  - component: {fileID: 1497258558}
  - component: {fileID: 1497258557}
  - component: {fileID: 1497258560}
  - component: {fileID: 1497258561}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1497258557
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1497258556}
  m_Enabled: 1
--- !u!20 &1497258558
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1497258556}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: 1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1497258559
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1497258556}
  m_LocalRotation: {x: -0, y: -0.18217868, z: -0, w: 0.9832655}
  m_LocalPosition: {x: 12.23, y: 2.042, z: -10.14}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 21
  m_LocalEulerAnglesHint: {x: 0, y: -20.993, z: 0}
--- !u!114 &1497258560
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1497258556}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 436275a13d4459746955fc6db5953473, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _active: 1
  _enableRotation: 1
  _mouseSense: 1.8
  _enableTranslation: 1
  _translationSpeed: 55
  _enableMovement: 1
  _movementSpeed: 10
  _boostedSpeed: 50
  _boostSpeed: 304
  _moveUp: 101
  _moveDown: 97
  _enableSpeedAcceleration: 1
  _speedAccelerationFactor: 1.5
  _initPositonButton: 114
--- !u!114 &1497258561
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1497258556}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 059fa2e9f55ed7b4ab8dffb8a402dfda, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  impactForce: 2
  Retry: 3
--- !u!1 &1625205268
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1625205272}
  - component: {fileID: 1625205271}
  - component: {fileID: 1625205270}
  - component: {fileID: 1625205269}
  m_Layer: 0
  m_Name: r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &1625205269
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1625205268}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1625205270
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1625205268}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1bb8d9be66ba1a84aa62fffc5126fa8c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1625205271
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1625205268}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1625205272
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1625205268}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.39936996, y: 1.9825153, z: -12.667373}
  m_LocalScale: {x: 20, y: 0.2, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 780728714}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1712239866
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.x
      value: -4.35
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.14
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.35
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Name
      value: glass (4)
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Layer
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_TagString
      value: Glass
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6617583239465076362, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 868f1ed900aac314f91480651543f59c, type: 3}
--- !u!1 &1712239867 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_PrefabInstance: {fileID: 1712239866}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1712239868
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1712239867}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 02cd666ee03082f419820ba60f8b1362, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Patterns:
  - {fileID: 4300000, guid: 32f121cbf83f79f45b5fe375c81caa1a, type: 2}
  - {fileID: 4300000, guid: 2cb3fb1ab07b00c4ca94a5bb73261a5a, type: 2}
--- !u!1 &1797030204
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1797030207}
  - component: {fileID: 1797030206}
  - component: {fileID: 1797030205}
  m_Layer: 1
  m_Name: Text (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!102 &1797030205
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1797030204}
  m_Text: Angle
  m_OffsetZ: 0
  m_CharacterSize: 1
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 0
  m_TabSize: 4
  m_FontSize: 64
  m_FontStyle: 0
  m_RichText: 1
  m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
  m_Color:
    serializedVersion: 2
    rgba: 4294967295
--- !u!23 &1797030206
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1797030204}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &1797030207
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1797030204}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 4.958, y: 5.53, z: -2.35}
  m_LocalScale: {x: 0.04, y: 0.04, z: 0.04}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 18
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1905302608
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1905302611}
  - component: {fileID: 1905302610}
  - component: {fileID: 1905302609}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1905302609
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1905302608}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f231c4fb786f3946a6b90b886c48677, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!114 &1905302610
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1905302608}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &1905302611
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1905302608}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 24
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1977696382
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1977696385}
  - component: {fileID: 1977696384}
  - component: {fileID: 1977696383}
  m_Layer: 1
  m_Name: Text (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!102 &1977696383
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1977696382}
  m_Text: Custom shape
  m_OffsetZ: 0
  m_CharacterSize: 1
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 0
  m_TabSize: 4
  m_FontSize: 64
  m_FontStyle: 0
  m_RichText: 1
  m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
  m_Color:
    serializedVersion: 2
    rgba: 4294967295
--- !u!23 &1977696384
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1977696382}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &1977696385
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1977696382}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.57, y: 3.07, z: -2.35}
  m_LocalScale: {x: 0.04, y: 0.04, z: 0.04}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 19
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1978072219
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1978072223}
  - component: {fileID: 1978072222}
  - component: {fileID: 1978072221}
  - component: {fileID: 1978072220}
  m_Layer: 0
  m_Name: Plane
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!64 &1978072220
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1978072219}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1978072221
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1978072219}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99a79aa0e7a1c624b88f5dc0e5ff1f9e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1978072222
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1978072219}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1978072223
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1978072219}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 5.9939, y: 5.9939, z: 5.9939}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 22
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2008606691
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2008606694}
  - component: {fileID: 2008606693}
  - component: {fileID: 2008606692}
  m_Layer: 1
  m_Name: Text (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!102 &2008606692
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008606691}
  m_Text: transform scale
  m_OffsetZ: 0
  m_CharacterSize: 1
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 0
  m_TabSize: 4
  m_FontSize: 64
  m_FontStyle: 0
  m_RichText: 1
  m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
  m_Color:
    serializedVersion: 2
    rgba: 4294967295
--- !u!23 &2008606693
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008606691}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &2008606694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008606691}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.67, y: 5.52, z: -2.35}
  m_LocalScale: {x: 0.04, y: 0.04, z: 0.04}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 16
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &2041193187
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_RootOrder
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.x
      value: 4.8700004
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 4.31
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.35
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9238794
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.3826839
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -45
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Name
      value: glass (6)
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Layer
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_TagString
      value: Glass
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6617583239465076362, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 868f1ed900aac314f91480651543f59c, type: 3}
--- !u!1 &2041193188 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_PrefabInstance: {fileID: 2041193187}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &2041193189
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2041193188}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 02cd666ee03082f419820ba60f8b1362, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Patterns:
  - {fileID: 4300000, guid: 32f121cbf83f79f45b5fe375c81caa1a, type: 2}
  - {fileID: 4300000, guid: 2cb3fb1ab07b00c4ca94a5bb73261a5a, type: 2}
--- !u!1 &2119999196
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2119999200}
  - component: {fileID: 2119999199}
  - component: {fileID: 2119999198}
  - component: {fileID: 2119999197}
  m_Layer: 0
  m_Name: r (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &2119999197
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2119999196}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &2119999198
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2119999196}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1bb8d9be66ba1a84aa62fffc5126fa8c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &2119999199
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2119999196}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &2119999200
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2119999196}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.39936996, y: -1.2174845, z: -10.267373}
  m_LocalScale: {x: 20, y: 6.5, z: 0.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 780728714}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2129097477
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2129097480}
  - component: {fileID: 2129097479}
  - component: {fileID: 2129097478}
  m_Layer: 1
  m_Name: Text (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!102 &2129097478
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2129097477}
  m_Text: Large
  m_OffsetZ: 0
  m_CharacterSize: 1
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 0
  m_TabSize: 4
  m_FontSize: 64
  m_FontStyle: 0
  m_RichText: 1
  m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
  m_Color:
    serializedVersion: 2
    rgba: 4294967295
--- !u!23 &2129097479
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2129097477}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &2129097480
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2129097477}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 9, y: 5.53, z: -2.35}
  m_LocalScale: {x: 0.04, y: 0.04, z: 0.04}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 27
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &325720165010329643
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.x
      value: 4.8700004
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.14
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.35
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 220546359785460484, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Name
      value: glass
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_Layer
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
      propertyPath: m_TagString
      value: Glass
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6617583239465076362, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 868f1ed900aac314f91480651543f59c, type: 3}
--- !u!1 &325720165010329644 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 595698986664643006, guid: 868f1ed900aac314f91480651543f59c, type: 3}
  m_PrefabInstance: {fileID: 325720165010329643}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &325720165010329645
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 325720165010329644}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 02cd666ee03082f419820ba60f8b1362, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Patterns:
  - {fileID: 4300000, guid: 32f121cbf83f79f45b5fe375c81caa1a, type: 2}
  - {fileID: 4300000, guid: 2cb3fb1ab07b00c4ca94a5bb73261a5a, type: 2}
